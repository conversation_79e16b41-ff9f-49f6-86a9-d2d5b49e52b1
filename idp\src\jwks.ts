import fs from "node:fs";
import path from "node:path";
import { createPublicKey } from "node:crypto";
import { exportJWK, importPKCS8, SignJWT, JWK } from "jose";
const keysDir = path.join(process.cwd(), "keys");
const privatePem = fs.readFileSync(path.join(keysDir, "private.pem"),
"utf8");
const publicPem = fs.readFileSync(path.join(keysDir, "public.pem"), "utf8");
// JWKS (public) — phát cho SP verify
export async function getJWKS(): Promise<{ keys: JWK[] }> {
const pubKey = createPublicKey(publicPem).export({ type: "spki", format:
"pem" }) as string;
const jwk = await exportJWK(await importPKCS8(privatePem, "RS256"));
// exportJWK từ private chứa d, chúng ta xoá các trường nh<PERSON><PERSON> cảm nếu có
delete (jwk as any).d;
// Thêm kid để SP có thể cache/rotate (demo hard-code)
jwk.kid = "dev-key-1";
jwk.alg = "RS256";
jwk.use = "sig";
return { keys: [jwk] };
}
export async function signAccessToken(payload: Record<string, any>, opts: {
issuer: string; audience: string; ttlSec: number; }): Promise<string> {
const { issuer, audience, ttlSec } = opts;
const now = Math.floor(Date.now() / 1000);
const pk = await importPKCS8(privatePem, "RS256");
return await new SignJWT(payload)
.setProtectedHeader({ alg: "RS256", kid: "dev-key-1" })
.setIssuer(issuer)
.setAudience(audience)
.setIssuedAt(now)
.setExpirationTime(now + ttlSec)
.sign(pk);
}
