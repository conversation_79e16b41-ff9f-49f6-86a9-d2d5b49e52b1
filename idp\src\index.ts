import "dotenv/config";
import express from "express";
import session from "express-session";
import cookieParser from "cookie-parser";
import { getJWKS, signAccessToken } from "./jwks";
import { verifyUser } from "./auth";
const app = express();
app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use(cookieParser());
app.use(session({
secret: process.env.SESSION_SECRET || "dev-secret-idp",
resave: false,
saveUninitialized: false,
}));

const PORT = Number(process.env.PORT || 4500);
const ISSUER = process.env.ISSUER || `http://localhost:${PORT}`;
const ACCESS_TOKEN_TTL = Number(process.env.ACCESS_TOKEN_TTL || 900);
const ALLOWED_AUDIENCES = (process.env.ALLOWED_AUDIENCES ||
"app1,app2").split(",").map(s => s.trim());
app.get("/", (req, res) => {
res.type("html").send(`<h1>IdP running</h1>
 <p>Login form: <a href=\"/login\">/login</a></p>
 <p>JWKS: <a href=\"/.well-known/jwks.json\">/.well-known/jwks.json</a></
p>`);
});
app.get("/.well-known/jwks.json", async (_req, res) => {
res.json(await getJWKS());
});
// Render login (đơn giản)
app.get("/login", (req, res) => {
const { redirect_uri = "", state = "", audience = "" } = req.query as any;
res.type("html").send(`
 <h2>SSO Login</h2>
 <form method=\"post\" action=\"/login\">
 <input type=\"hidden\" name=\"redirect_uri\" value=\"${redirect_uri}
\" />
 <input type=\"hidden\" name=\"state\" value=\"${state}\" />
 <input type=\"hidden\" name=\"audience\" value=\"${audience}\" />
 <label>Email <input name=\"email\" value=\"<EMAIL>\"/></
label><br/>
 <label>Password <input type=\"password\" name=\"password\"
value=\"123456\"/></label><br/>
 <button type=\"submit\">Login</button>
 </form>
 `);
});
// Xử lý login
app.post("/login", async (req, res) => {
const { email, password, redirect_uri, state, audience } = req.body as any;
const user = verifyUser(email, password);
if (!user) return res.status(401).send("Invalid credentials");
// lưu session đăng nhập tại IdP
(req.session as any).user = { id: user.id, email: user.email, name:
user.name };
if (!redirect_uri) return res.send("Logged in at IdP. No redirect_uriprovided.");
if (!audience || !ALLOWED_AUDIENCES.includes(audience)) return
res.status(400).send("Invalid audience");
const token = await signAccessToken(
{ sub: user.id, email: user.email, name: user.name },
{ issuer: ISSUER, audience, ttlSec: ACCESS_TOKEN_TTL }
);
const url = new URL(redirect_uri);
url.searchParams.set("token", token);
if (state) url.searchParams.set("state", state);
res.redirect(url.toString());
});
// /authorize: nếu đã có session tại IdP thì phát token luôn (SSO), ngược lại render login
app.get("/authorize", async (req, res) => {
const { redirect_uri, state, audience } = req.query as any;
if (!redirect_uri) return res.status(400).send("redirect_uri required");
if (!audience || !ALLOWED_AUDIENCES.includes(audience)) return
res.status(400).send("Invalid audience");
const sessUser = (req.session as any).user;
if (!sessUser) {
// chưa login ở IdP → hiển thị form login kèm tham số
const loginUrl = new URL("/login", ISSUER);
loginUrl.searchParams.set("redirect_uri", redirect_uri);
if (state) loginUrl.searchParams.set("state", String(state));
loginUrl.searchParams.set("audience", audience);
return res.redirect(loginUrl.toString());
}
// đã có SSO session → cấp token ngay
const token = await signAccessToken(
{ sub: sessUser.id, email: sessUser.email, name: sessUser.name },
{ issuer: ISSUER, audience, ttlSec: ACCESS_TOKEN_TTL }
);
const url = new URL(String(redirect_uri));
if (state) url.searchParams.set("state", String(state));
url.searchParams.set("token", token);
res.redirect(url.toString());
});
app.post("/logout", (req, res) => {
req.session.destroy(() => {
res.send("Logged out at IdP (global)");
});
});
app.listen(PORT, () => {
console.log(`IdP listening on ${ISSUER}`);
});