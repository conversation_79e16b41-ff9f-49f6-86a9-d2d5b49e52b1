{"name": "@types/express", "version": "4.17.23", "description": "TypeScript definitions for express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/puneetar"}, {"name": "<PERSON>", "githubUsername": "dfrankland", "url": "https://github.com/dfrankland"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "c7c276342e7f5808be5916c5dec3e614fa8b6e60b3323e369f793c389ee6f860", "typeScriptVersion": "5.1"}