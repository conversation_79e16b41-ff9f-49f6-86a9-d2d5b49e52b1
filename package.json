{"name": "sp", "private": true, "type": "module", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts"}, "dependencies": {"cookie-parser": "^1.4.6", "dotenv": "^16.4.5", "express": "^4.19.2", "express-session": "^1.17.3", "jose": "^5.9.3", "uuid": "^11.0.3"}, "devDependencies": {"@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.21", "@types/express-session": "^1.17.8", "@types/node": "^20.14.9", "@types/uuid": "^10.0.0", "ts-node-dev": "^2.0.0", "typescript": "^5.5.4"}}